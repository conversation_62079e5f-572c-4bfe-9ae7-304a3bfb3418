# Attendance
so by <PERSON>'s <PERSON>, the webapp tracks students' and staff's daily attendance
new attendance object
s: 'a' // tenant id for attendance points
d: 1 or 0 // direction of this attendance record, 1 means in i.e sign in, 0 means out
t: timestamp
u: user id // uuid of a `sch_usr` point
sc: school uuid


sign in page /sc/:i/ati
scans a qr code that resolves to the `sch_usr` uuid from the query param i, by God's grace
an attendance record is created with `d: 1` and `t: new Date()`, u the uuid the qr code resolves to, and sc the school uuid

sign out page /sc/:i/p/o
scans a qr code that resolves to the `sch_usr` uuid from the query param i, by God's grace
an attendance record is created with `d: 0` and `t: new Date()`, u the uuid the qr code resolves to, and sc the school uuid

/u/:i/s user settings page
only one button for now, enable push notifications. alongside the button, an explanation of why it's useful to turn push notifications for the attendance notification feature is given
on clicking the button, a request is made w/ JS web APIs to enable push notifications for the user
on accept, a push notification subscription is made for the user, and it's details are saved to the db, using the same convention of minimal key/field names (1 or 2 characters) for db objects, and a tenant id of `n` for notifications
on success a toast message is shown to the user
on reject, the user is warned (a beautifully styled, calm, subtle warning modal) that if they don't activate push notifications, they won't receive notifications for attendance

feature
on sign in and sign out, a respective notification is sent to the user's phone, concisely saying the notification type and the time of the action, so it fits nicely in the push notification's usually limited practial viewing area

school attendance page /sc/:i/p
two buttons, 'sign users in', 'sign users out', that goes to /sc/:i/si and /sc/:i/so respectively

get qr code /u/:i/qr
creates a qr code that resolves to the uuid from query param `i`

user attendance records /u/:i/:s/a
shows all attendance records for the user for that school, with sign in and sign out times
each row looks like this:
    date | sign in time | sign out time
    Friday, July 6, 2001 | 08:00 | 17:00
date range filter
`only sign ins` filter
`only sign outs` filter

---

give me a plan first before executing